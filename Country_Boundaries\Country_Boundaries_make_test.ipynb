# 导入必要的库
import os
import numpy as np
import pandas as pd
import geopandas as gpd
import rasterio
from rasterio.features import rasterize
from rasterio.mask import mask
from rasterio.transform import from_bounds
from shapely.geometry import box
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

print("库导入完成！")

# 数据路径设置
# 输入数据路径
world_boundaries_path = r"Z:\yuan\paper3_new02\shp\World_Country_Boundaries_GS(2021)6375\World_Country_Boundaries.shp"
basins_path = r"Z:\yuan\paper3_new02\shp\basins_lev01-12\basins_lev05.shp"
raster_path = r"Z:\yuan\ERA5-Land\fbl_025\EPEs\yz_600_1440\pre_yz_90_1971_2020.tif"

# 输出路径
output_dir = r"Z:\yuan\paper3_new02\shp\basin_Country"

# 创建输出目录
os.makedirs(output_dir, exist_ok=True)

print(f"输出目录已创建：{output_dir}")
print("\n数据路径设置完成！")

# 读取世界国家边界数据
print("正在读取世界国家边界数据...")
world_countries = gpd.read_file(world_boundaries_path)
print(f"世界国家边界数据读取完成，共{len(world_countries)}个国家")
print(f"国家边界坐标系：{world_countries.crs}")
print(f"国家ID字段：NR_C_ID")
print(f"国家边界数据列：{list(world_countries.columns)}")
print("\n前5个国家信息：")
print(world_countries[['NR_C_ID', 'geometry']].head())

# 读取研究区流域数据
print("正在读取研究区流域数据...")
basins = gpd.read_file(basins_path)
print(f"流域数据读取完成，共{len(basins)}个流域")
print(f"流域坐标系：{basins.crs}")
print(f"流域ID字段：HYBAS_ID")
print(f"流域数据列：{list(basins.columns)}")
print("\n前5个流域信息：")
print(basins[['HYBAS_ID', 'geometry']].head())

# 读取栅格数据
print("正在读取栅格数据...")
with rasterio.open(raster_path) as src:
    raster_data = src.read(1)  # 读取第一个波段
    raster_transform = src.transform
    raster_crs = src.crs
    raster_bounds = src.bounds
    raster_shape = raster_data.shape
    
print(f"栅格数据读取完成")
print(f"栅格坐标系：{raster_crs}")
print(f"栅格形状：{raster_shape}")
print(f"栅格范围：{raster_bounds}")
print(f"栅格变换参数：{raster_transform}")

# 统计有效像元（>=0）
valid_pixels = raster_data >= 0
valid_count = np.sum(valid_pixels)
total_count = raster_data.size
print(f"\n栅格统计：")
print(f"总像元数：{total_count}")
print(f"有效像元数（>=0）：{valid_count}")
print(f"有效像元比例：{valid_count/total_count:.2%}")
print(f"栅格数据范围：{np.nanmin(raster_data):.2f} ~ {np.nanmax(raster_data):.2f}")

# 检查和统一坐标系统
print("检查坐标系统...")
print(f"世界国家边界坐标系：{world_countries.crs}")
print(f"流域坐标系：{basins.crs}")
print(f"栅格坐标系：{raster_crs}")

# 如果坐标系不一致，需要转换
target_crs = raster_crs  # 以栅格数据的坐标系为准

if world_countries.crs != target_crs:
    print(f"\n转换世界国家边界坐标系从 {world_countries.crs} 到 {target_crs}")
    world_countries = world_countries.to_crs(target_crs)
    
if basins.crs != target_crs:
    print(f"转换流域坐标系从 {basins.crs} 到 {target_crs}")
    basins = basins.to_crs(target_crs)

print("\n坐标系统统一完成！")

# 创建研究区的总边界（所有流域的联合边界）
print("创建研究区总边界...")
study_area = basins.unary_union
study_area_gdf = gpd.GeoDataFrame([1], geometry=[study_area], crs=basins.crs)
print(f"研究区总边界创建完成")

# 保存研究区边界
study_area_path = os.path.join(output_dir, "study_area_boundary.shp")
study_area_gdf.to_file(study_area_path)
print(f"研究区边界已保存：{study_area_path}")

# 找出与研究区相交的国家
print("\n查找与研究区相交的国家...")
# 使用空间索引加速查询
intersecting_countries = world_countries[world_countries.intersects(study_area)]

print(f"找到{len(intersecting_countries)}个与研究区相交的国家")
print("\n相交国家列表：")
if 'NAME' in intersecting_countries.columns:
    for idx, row in intersecting_countries.iterrows():
        print(f"国家ID: {row['NR_C_ID']}, 国家名: {row.get('NAME', 'Unknown')}")
else:
    for idx, row in intersecting_countries.iterrows():
        print(f"国家ID: {row['NR_C_ID']}")

# 保存相交的国家边界
intersecting_countries_path = os.path.join(output_dir, "intersecting_countries.shp")
intersecting_countries.to_file(intersecting_countries_path)
print(f"\n相交国家边界已保存：{intersecting_countries_path}")

# 可视化研究区和相交国家
print("\n创建可视化图...")
fig, ax = plt.subplots(1, 1, figsize=(15, 10))

# 绘制相交的国家边界
intersecting_countries.plot(ax=ax, color='lightblue', edgecolor='blue', alpha=0.7, label='相交国家')

# 绘制研究区流域
basins.plot(ax=ax, color='red', alpha=0.5, edgecolor='darkred', label='研究区流域')

# 绘制研究区总边界
study_area_gdf.plot(ax=ax, color='none', edgecolor='black', linewidth=2, label='研究区边界')

ax.set_title('研究区与相交国家分布图', fontsize=16)
ax.legend()
ax.set_xlabel('经度')
ax.set_ylabel('纬度')

# 保存图片
fig_path = os.path.join(output_dir, "study_area_countries_overview.png")
plt.savefig(fig_path, dpi=300, bbox_inches='tight')
plt.show()
print(f"可视化图已保存：{fig_path}")

# 计算每个国家与研究区的重叠面积
print("计算国家与研究区重叠面积...")

# 创建结果列表
overlap_results = []

for idx, country in intersecting_countries.iterrows():
    country_id = country['NR_C_ID']
    country_name = country.get('NAME', f'Country_{country_id}')
    country_geom = country.geometry
    
    # 计算国家总面积（平方米）
    country_area = country_geom.area
    
    # 计算与研究区的交集
    intersection = country_geom.intersection(study_area)
    
    # 计算重叠面积（平方米）
    overlap_area = intersection.area if not intersection.is_empty else 0
    
    # 计算重叠比例
    overlap_ratio = overlap_area / country_area if country_area > 0 else 0
    
    # 转换为平方公里
    country_area_km2 = country_area / 1e6
    overlap_area_km2 = overlap_area / 1e6
    
    overlap_results.append({
        'Country_ID': country_id,
        'Country_Name': country_name,
        'Country_Area_km2': country_area_km2,
        'Overlap_Area_km2': overlap_area_km2,
        'Overlap_Ratio': overlap_ratio,
        'geometry': intersection if not intersection.is_empty else None
    })
    
    print(f"国家ID {country_id} ({country_name}): "
          f"总面积 {country_area_km2:.2f} km², "
          f"重叠面积 {overlap_area_km2:.2f} km², "
          f"重叠比例 {overlap_ratio:.2%}")

print(f"\n完成{len(overlap_results)}个国家的重叠面积计算")

# 创建重叠结果的DataFrame和GeoDataFrame
overlap_df = pd.DataFrame(overlap_results)
overlap_df = overlap_df.sort_values('Overlap_Area_km2', ascending=False)

# 保存重叠统计结果
overlap_stats_path = os.path.join(output_dir, "country_overlap_statistics.csv")
overlap_df[['Country_ID', 'Country_Name', 'Country_Area_km2', 'Overlap_Area_km2', 'Overlap_Ratio']].to_csv(
    overlap_stats_path, index=False, encoding='utf-8-sig')
print(f"重叠统计结果已保存：{overlap_stats_path}")

# 创建重叠区域的GeoDataFrame（只包含有重叠的区域）
overlap_geoms = [geom for geom in overlap_df['geometry'] if geom is not None]
if overlap_geoms:
    overlap_gdf = gpd.GeoDataFrame(
        overlap_df[overlap_df['geometry'].notna()].drop('geometry', axis=1),
        geometry=overlap_geoms,
        crs=basins.crs
    )
    
    # 保存重叠区域shapefile
    overlap_regions_path = os.path.join(output_dir, "country_overlap_regions.shp")
    overlap_gdf.to_file(overlap_regions_path)
    print(f"重叠区域shapefile已保存：{overlap_regions_path}")

# 显示统计摘要
print("\n=== 重叠面积统计摘要 ===")
print(f"总研究区面积：{study_area.area/1e6:.2f} km²")
print(f"涉及国家数量：{len(overlap_df)}")
print(f"总重叠面积：{overlap_df['Overlap_Area_km2'].sum():.2f} km²")
print("\n前5个重叠面积最大的国家：")
print(overlap_df[['Country_ID', 'Country_Name', 'Overlap_Area_km2', 'Overlap_Ratio']].head())

# 分析每个流域的有效像元数量
print("分析每个流域的有效像元数量...")

# 为每个流域创建掩膜并统计有效像元
basin_pixel_stats = []

# 打开栅格文件以获取完整的地理信息
with rasterio.open(raster_path) as src:
    for idx, basin in basins.iterrows():
        basin_id = basin['HYBAS_ID']
        basin_geom = basin.geometry
        
        try:
            # 使用流域几何体裁剪栅格
            masked_data, masked_transform = mask(src, [basin_geom], crop=True, nodata=np.nan)
            masked_data = masked_data[0]  # 取第一个波段
            
            # 统计有效像元（>=0）
            valid_mask = masked_data >= 0
            valid_pixels_count = np.sum(valid_mask)
            total_pixels_count = np.sum(~np.isnan(masked_data))
            
            basin_pixel_stats.append({
                'Basin_ID': basin_id,
                'Total_Pixels': total_pixels_count,
                'Valid_Pixels': valid_pixels_count,
                'Valid_Ratio': valid_pixels_count / total_pixels_count if total_pixels_count > 0 else 0
            })
            
            print(f"流域 {basin_id}: 总像元 {total_pixels_count}, 有效像元 {valid_pixels_count}, "
                  f"有效比例 {valid_pixels_count/total_pixels_count:.2%}" if total_pixels_count > 0 else f"流域 {basin_id}: 无像元")
            
        except Exception as e:
            print(f"处理流域 {basin_id} 时出错: {e}")
            basin_pixel_stats.append({
                'Basin_ID': basin_id,
                'Total_Pixels': 0,
                'Valid_Pixels': 0,
                'Valid_Ratio': 0
            })

print(f"\n完成{len(basin_pixel_stats)}个流域的像元统计")

# 保存流域像元统计结果
basin_stats_df = pd.DataFrame(basin_pixel_stats)
basin_stats_df = basin_stats_df.sort_values('Valid_Pixels', ascending=False)

basin_stats_path = os.path.join(output_dir, "basin_pixel_statistics.csv")
basin_stats_df.to_csv(basin_stats_path, index=False, encoding='utf-8-sig')
print(f"流域像元统计结果已保存：{basin_stats_path}")

# 显示统计摘要
print("\n=== 流域像元统计摘要 ===")
print(f"流域总数：{len(basin_stats_df)}")
print(f"总有效像元数：{basin_stats_df['Valid_Pixels'].sum()}")
print(f"平均每个流域有效像元数：{basin_stats_df['Valid_Pixels'].mean():.0f}")
print("\n前5个有效像元最多的流域：")
print(basin_stats_df[['Basin_ID', 'Valid_Pixels', 'Valid_Ratio']].head())

# 生成流域ID栅格（只在有效像元位置赋值流域ID）
print("生成流域ID栅格...")

with rasterio.open(raster_path) as src:
    # 创建输出栅格，初始化为nodata
    basin_id_raster = np.full(src.shape, src.nodata, dtype=np.float32)
    
    # 读取原始栅格数据
    original_data = src.read(1)
    
    # 只处理有效像元（>=0）
    valid_mask = original_data >= 0
    
    print(f"原始栅格中有效像元数量：{np.sum(valid_mask)}")
    
    # 为每个流域分配ID
    processed_pixels = 0
    
    for idx, basin in basins.iterrows():
        basin_id = basin['HYBAS_ID']
        basin_geom = basin.geometry
        
        try:
            # 创建流域掩膜
            basin_mask = rasterize(
                [basin_geom],
                out_shape=src.shape,
                transform=src.transform,
                fill=0,
                default_value=1,
                dtype=np.uint8
            )
            
            # 找到同时满足有效像元和流域范围的像元
            basin_valid_mask = valid_mask & (basin_mask == 1)
            basin_pixel_count = np.sum(basin_valid_mask)
            
            if basin_pixel_count > 0:
                # 在这些位置赋值流域ID
                basin_id_raster[basin_valid_mask] = basin_id
                processed_pixels += basin_pixel_count
                print(f"流域 {basin_id}: 分配了 {basin_pixel_count} 个有效像元")
            
        except Exception as e:
            print(f"处理流域 {basin_id} 时出错: {e}")
    
    print(f"\n总共处理了 {processed_pixels} 个有效像元")
    
    # 保存流域ID栅格
    basin_id_raster_path = os.path.join(output_dir, "basin_id_raster.tif")
    
    # 更新profile
    profile = src.profile.copy()
    profile.update({
        'dtype': rasterio.float32,
        'nodata': -9999,
        'compress': 'lzw'
    })
    
    # 将nodata值设置为-9999
    basin_id_raster[basin_id_raster == src.nodata] = -9999
    
    with rasterio.open(basin_id_raster_path, 'w', **profile) as dst:
        dst.write(basin_id_raster, 1)
    
    print(f"流域ID栅格已保存：{basin_id_raster_path}")
    
    # 统计结果
    unique_ids = np.unique(basin_id_raster[basin_id_raster != -9999])
    print(f"栅格中包含的流域ID数量：{len(unique_ids)}")
    print(f"流域ID范围：{unique_ids.min():.0f} - {unique_ids.max():.0f}")

# 生成国家ID栅格（只在有效像元位置赋值国家ID）
print("生成国家ID栅格...")

with rasterio.open(raster_path) as src:
    # 创建输出栅格，初始化为nodata
    country_id_raster = np.full(src.shape, -9999, dtype=np.float32)
    
    # 读取原始栅格数据
    original_data = src.read(1)
    
    # 只处理有效像元（>=0）
    valid_mask = original_data >= 0
    
    print(f"原始栅格中有效像元数量：{np.sum(valid_mask)}")
    
    # 统计每个国家的有效像元数量
    country_pixel_stats = []
    processed_pixels = 0
    
    for idx, country in intersecting_countries.iterrows():
        country_id = country['NR_C_ID']
        country_name = country.get('NAME', f'Country_{country_id}')
        country_geom = country.geometry
        
        try:
            # 创建国家掩膜
            country_mask = rasterize(
                [country_geom],
                out_shape=src.shape,
                transform=src.transform,
                fill=0,
                default_value=1,
                dtype=np.uint8
            )
            
            # 找到同时满足有效像元和国家范围的像元
            country_valid_mask = valid_mask & (country_mask == 1)
            country_pixel_count = np.sum(country_valid_mask)
            
            if country_pixel_count > 0:
                # 在这些位置赋值国家ID
                country_id_raster[country_valid_mask] = country_id
                processed_pixels += country_pixel_count
                
                country_pixel_stats.append({
                    'Country_ID': country_id,
                    'Country_Name': country_name,
                    'Valid_Pixels': country_pixel_count
                })
                
                print(f"国家 {country_id} ({country_name}): 分配了 {country_pixel_count} 个有效像元")
            else:
                country_pixel_stats.append({
                    'Country_ID': country_id,
                    'Country_Name': country_name,
                    'Valid_Pixels': 0
                })
            
        except Exception as e:
            print(f"处理国家 {country_id} 时出错: {e}")
            country_pixel_stats.append({
                'Country_ID': country_id,
                'Country_Name': country_name,
                'Valid_Pixels': 0
            })
    
    print(f"\n总共处理了 {processed_pixels} 个有效像元")

# 保存国家ID栅格
country_id_raster_path = os.path.join(output_dir, "country_id_raster.tif")

# 更新profile
profile = src.profile.copy()
profile.update({
    'dtype': rasterio.float32,
    'nodata': -9999,
    'compress': 'lzw'
})

with rasterio.open(country_id_raster_path, 'w', **profile) as dst:
    dst.write(country_id_raster, 1)

print(f"国家ID栅格已保存：{country_id_raster_path}")

# 统计结果
unique_country_ids = np.unique(country_id_raster[country_id_raster != -9999])
print(f"栅格中包含的国家ID数量：{len(unique_country_ids)}")
if len(unique_country_ids) > 0:
    print(f"国家ID范围：{unique_country_ids.min():.0f} - {unique_country_ids.max():.0f}")

# 保存国家像元统计结果
country_pixel_df = pd.DataFrame(country_pixel_stats)
country_pixel_df = country_pixel_df.sort_values('Valid_Pixels', ascending=False)

country_pixel_stats_path = os.path.join(output_dir, "country_pixel_statistics.csv")
country_pixel_df.to_csv(country_pixel_stats_path, index=False, encoding='utf-8-sig')
print(f"国家像元统计结果已保存：{country_pixel_stats_path}")

# 显示统计摘要
print("\n=== 国家像元统计摘要 ===")
print(f"涉及国家数量：{len(country_pixel_df)}")
print(f"总有效像元数：{country_pixel_df['Valid_Pixels'].sum()}")
print(f"平均每个国家有效像元数：{country_pixel_df['Valid_Pixels'].mean():.0f}")
print("\n前5个有效像元最多的国家：")
print(country_pixel_df[['Country_ID', 'Country_Name', 'Valid_Pixels']].head())

# 生成综合分析报告
print("生成综合分析报告...")

report_content = f"""
# 地理空间数据分析报告

## 分析概述
本报告分析了世界国家边界与研究区流域的重叠关系，以及有效像元的分布情况。

## 数据源
- 世界国家边界：{world_boundaries_path}
- 研究区流域：{basins_path}
- 栅格数据：{raster_path}

## 主要结果

### 1. 研究区基本信息
- 研究区总面积：{study_area.area/1e6:.2f} km²
- 包含流域数量：{len(basins)}
- 栅格总像元数：{raster_data.size}
- 有效像元数（>=0）：{np.sum(raster_data >= 0)}
- 有效像元比例：{np.sum(raster_data >= 0)/raster_data.size:.2%}

### 2. 国家重叠分析
- 涉及国家数量：{len(intersecting_countries)}
- 总重叠面积：{overlap_df['Overlap_Area_km2'].sum():.2f} km²
- 重叠面积最大的前3个国家：
"""

# 添加前3个重叠面积最大的国家
top3_countries = overlap_df.head(3)
for idx, row in top3_countries.iterrows():
    report_content += f"  - {row['Country_Name']} (ID: {row['Country_ID']}): {row['Overlap_Area_km2']:.2f} km² ({row['Overlap_Ratio']:.2%})\n"

report_content += f"""

### 3. 流域像元分析
- 流域总数：{len(basin_stats_df)}
- 总有效像元数：{basin_stats_df['Valid_Pixels'].sum()}
- 平均每个流域有效像元数：{basin_stats_df['Valid_Pixels'].mean():.0f}
- 有效像元最多的前3个流域：
"""

# 添加前3个有效像元最多的流域
top3_basins = basin_stats_df.head(3)
for idx, row in top3_basins.iterrows():
    report_content += f"  - 流域ID {row['Basin_ID']}: {row['Valid_Pixels']} 个有效像元 ({row['Valid_Ratio']:.2%})\n"

report_content += f"""

### 4. 国家像元分析
- 涉及国家数量：{len(country_pixel_df)}
- 总有效像元数：{country_pixel_df['Valid_Pixels'].sum()}
- 平均每个国家有效像元数：{country_pixel_df['Valid_Pixels'].mean():.0f}
- 有效像元最多的前3个国家：
"""

# 添加前3个有效像元最多的国家
top3_country_pixels = country_pixel_df.head(3)
for idx, row in top3_country_pixels.iterrows():
    report_content += f"  - {row['Country_Name']} (ID: {row['Country_ID']}): {row['Valid_Pixels']} 个有效像元\n"

report_content += f"""

## 输出文件
所有结果文件已保存到：{output_dir}

### 矢量数据
- study_area_boundary.shp: 研究区总边界
- intersecting_countries.shp: 与研究区相交的国家边界
- country_overlap_regions.shp: 国家与研究区的重叠区域

### 栅格数据
- basin_id_raster.tif: 流域ID栅格（像元值为流域ID）
- country_id_raster.tif: 国家ID栅格（像元值为国家ID）

### 统计数据
- country_overlap_statistics.csv: 国家重叠面积统计
- basin_pixel_statistics.csv: 流域像元统计
- country_pixel_statistics.csv: 国家像元统计

### 可视化
- study_area_countries_overview.png: 研究区与相交国家分布图
"""

# 保存报告
report_path = os.path.join(output_dir, "analysis_report.md")
with open(report_path, 'w', encoding='utf-8') as f:
    f.write(report_content)

print(f"综合分析报告已保存：{report_path}")

# 创建数据验证和质量检查
print("\n=== 数据验证和质量检查 ===")

# 1. 检查栅格数据一致性
print("\n1. 栅格数据一致性检查：")
with rasterio.open(os.path.join(output_dir, "basin_id_raster.tif")) as basin_src:
    basin_raster = basin_src.read(1)
    
with rasterio.open(os.path.join(output_dir, "country_id_raster.tif")) as country_src:
    country_raster = country_src.read(1)

# 检查有效像元数量
basin_valid = np.sum(basin_raster != -9999)
country_valid = np.sum(country_raster != -9999)
original_valid = np.sum(raster_data >= 0)

print(f"原始栅格有效像元数：{original_valid}")
print(f"流域ID栅格有效像元数：{basin_valid}")
print(f"国家ID栅格有效像元数：{country_valid}")
print(f"流域栅格覆盖率：{basin_valid/original_valid:.2%}")
print(f"国家栅格覆盖率：{country_valid/original_valid:.2%}")

# 2. 检查ID范围
print("\n2. ID范围检查：")
basin_ids_in_raster = np.unique(basin_raster[basin_raster != -9999])
basin_ids_in_shp = basins['HYBAS_ID'].unique()
country_ids_in_raster = np.unique(country_raster[country_raster != -9999])
country_ids_in_shp = intersecting_countries['NR_C_ID'].unique()

print(f"Shapefile中流域ID数量：{len(basin_ids_in_shp)}")
print(f"栅格中流域ID数量：{len(basin_ids_in_raster)}")
print(f"Shapefile中国家ID数量：{len(country_ids_in_shp)}")
print(f"栅格中国家ID数量：{len(country_ids_in_raster)}")

# 3. 检查数据完整性
print("\n3. 数据完整性检查：")
missing_basin_ids = set(basin_ids_in_shp) - set(basin_ids_in_raster)
missing_country_ids = set(country_ids_in_shp) - set(country_ids_in_raster)

if missing_basin_ids:
    print(f"警告：{len(missing_basin_ids)}个流域ID在栅格中缺失")
else:
    print("✓ 所有流域ID都在栅格中存在")
    
if missing_country_ids:
    print(f"警告：{len(missing_country_ids)}个国家ID在栅格中缺失")
else:
    print("✓ 所有国家ID都在栅格中存在")

print("\n=== 分析完成 ===")
print(f"所有结果已保存到：{output_dir}")
print("请查看生成的文件和报告以获取详细结果。")